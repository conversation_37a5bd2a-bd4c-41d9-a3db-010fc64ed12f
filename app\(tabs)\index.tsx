import React from "react";
import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
} from "react-native";

export default function HomeScreen() {
  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.location}>📍 Cape Town, South Africa</Text>
          <Text style={styles.greeting}>Hello there!</Text>
          <Text style={styles.subtitle}>Find trusted service providers near you</Text>
        </View>

        {/* Search Bar */}
        <TouchableOpacity style={styles.searchContainer}>
          <View style={styles.searchBar}>
            <Text style={styles.searchIcon}>🔍</Text>
            <Text style={styles.searchPlaceholder}>What service do you need?</Text>
          </View>
        </TouchableOpacity>

        {/* Promotional Banner */}
        <View style={styles.promoBanner}>
          <View style={styles.promoContent}>
            <Text style={styles.promoIcon}>🎁</Text>
            <View style={styles.promoText}>
              <Text style={styles.promoTitle}>Need a helping hand?</Text>
              <Text style={styles.promoSubtitle}>Get 15% off your first booking</Text>
            </View>
          </View>
          <View style={styles.promoButton}>
            <Text style={styles.promoButtonText}>Book Now</Text>
          </View>
        </View>

        {/* Categories Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Categories</Text>
            <Text style={styles.seeAllText}>See All</Text>
          </View>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.categoriesList}>
            <View style={styles.categoryCard}>
              <View style={styles.categoryIcon}>
                <Text style={styles.categoryEmoji}>🏠</Text>
              </View>
              <Text style={styles.categoryName}>Home Services</Text>
            </View>
            <View style={styles.categoryCard}>
              <View style={styles.categoryIcon}>
                <Text style={styles.categoryEmoji}>✂️</Text>
              </View>
              <Text style={styles.categoryName}>Personal Care</Text>
            </View>
            <View style={styles.categoryCard}>
              <View style={styles.categoryIcon}>
                <Text style={styles.categoryEmoji}>💼</Text>
              </View>
              <Text style={styles.categoryName}>Professional</Text>
            </View>
            <View style={styles.categoryCard}>
              <View style={styles.categoryIcon}>
                <Text style={styles.categoryEmoji}>📚</Text>
              </View>
              <Text style={styles.categoryName}>Education</Text>
            </View>
          </ScrollView>
        </View>

        {/* Featured Providers Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Featured Providers</Text>
            <Text style={styles.seeAllText}>See All</Text>
          </View>

          <View style={styles.providerCard}>
            <View style={styles.providerHeader}>
              <View style={styles.providerAvatar} />
              <View style={styles.providerInfo}>
                <Text style={styles.providerName}>Thabo's Cleaning Services ✓</Text>
                <Text style={styles.providerLocation}>📍 Cape Town • 3.2 km</Text>
              </View>
            </View>
            <Text style={styles.providerDescription}>Professional cleaning services with 5+ years of experience.</Text>
            <View style={styles.providerFooter}>
              <Text style={styles.rating}>⭐ 4.8 (124)</Text>
              <View style={styles.priceContainer}>
                <Text style={styles.priceLabel}>From</Text>
                <Text style={styles.price}>R350</Text>
              </View>
            </View>
          </View>

          <View style={styles.providerCard}>
            <View style={styles.providerHeader}>
              <View style={styles.providerAvatar} />
              <View style={styles.providerInfo}>
                <Text style={styles.providerName}>Sipho's Electrical ✓</Text>
                <Text style={styles.providerLocation}>📍 Durban • 4.1 km</Text>
              </View>
            </View>
            <Text style={styles.providerDescription}>Licensed electrician with 10+ years of experience.</Text>
            <View style={styles.providerFooter}>
              <Text style={styles.rating}>⭐ 4.9 (156)</Text>
              <View style={styles.priceContainer}>
                <Text style={styles.priceLabel}>From</Text>
                <Text style={styles.price}>R450</Text>
              </View>
            </View>
          </View>
        </View>

        {/* Nearby Providers Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Nearby Providers</Text>
            <Text style={styles.seeAllText}>See All</Text>
          </View>

          <View style={styles.providerCard}>
            <View style={styles.providerHeader}>
              <View style={styles.providerAvatar} />
              <View style={styles.providerInfo}>
                <Text style={styles.providerName}>Lerato's Beauty ✓</Text>
                <Text style={styles.providerLocation}>📍 Pretoria • 2.8 km</Text>
              </View>
            </View>
            <Text style={styles.providerDescription}>Certified beautician specializing in natural hair care.</Text>
            <View style={styles.providerFooter}>
              <Text style={styles.rating}>⭐ 4.7 (92)</Text>
              <View style={styles.priceContainer}>
                <Text style={styles.priceLabel}>From</Text>
                <Text style={styles.price}>R280</Text>
              </View>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F9FAFB",
  },
  scrollView: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 16,
  },
  location: {
    fontSize: 14,
    color: "#6B7280",
    marginBottom: 8,
  },
  greeting: {
    fontSize: 28,
    fontWeight: "bold",
    marginBottom: 4,
    color: "#1F2937",
  },
  subtitle: {
    fontSize: 16,
    color: "#6B7280",
  },
  searchContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  searchBar: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#FFFFFF",
    borderRadius: 8,
    padding: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  searchIcon: {
    fontSize: 16,
    marginRight: 8,
  },
  searchPlaceholder: {
    fontSize: 16,
    color: "#6B7280",
    flex: 1,
  },
  promoBanner: {
    backgroundColor: "#10B981",
    marginHorizontal: 20,
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  promoContent: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  promoIcon: {
    fontSize: 24,
    marginRight: 12,
  },
  promoText: {
    flex: 1,
  },
  promoTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#FFFFFF",
    marginBottom: 2,
  },
  promoSubtitle: {
    fontSize: 14,
    color: "#FFFFFF",
    opacity: 0.9,
  },
  promoButton: {
    backgroundColor: "#FFFFFF",
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  promoButtonText: {
    fontSize: 14,
    color: "#10B981",
    fontWeight: "600",
  },
  section: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: "600",
    color: "#1F2937",
  },
  seeAllText: {
    fontSize: 14,
    color: "#10B981",
    fontWeight: "500",
  },
  categoriesList: {
    paddingHorizontal: 20,
  },
  categoryCard: {
    alignItems: "center",
    marginRight: 16,
    width: 80,
  },
  categoryIcon: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: "#FFFFFF",
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  categoryEmoji: {
    fontSize: 24,
  },
  categoryName: {
    fontSize: 12,
    textAlign: "center",
    color: "#1F2937",
    fontWeight: "500",
  },
  providerCard: {
    backgroundColor: "#FFFFFF",
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 20,
    marginBottom: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  providerHeader: {
    flexDirection: "row",
    marginBottom: 12,
  },
  providerAvatar: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: "#F3F4F6",
    marginRight: 12,
  },
  providerInfo: {
    flex: 1,
  },
  providerName: {
    fontSize: 18,
    fontWeight: "600",
    color: "#1F2937",
    marginBottom: 4,
  },
  providerLocation: {
    fontSize: 14,
    color: "#6B7280",
  },
  providerDescription: {
    fontSize: 14,
    color: "#6B7280",
    marginBottom: 12,
    lineHeight: 20,
  },
  providerFooter: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  rating: {
    fontSize: 14,
    fontWeight: "500",
    color: "#1F2937",
  },
  priceContainer: {
    alignItems: "flex-end",
  },
  priceLabel: {
    fontSize: 12,
    color: "#6B7280",
  },
  price: {
    fontSize: 16,
    fontWeight: "600",
    color: "#10B981",
  },
});