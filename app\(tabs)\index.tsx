import React, { useState } from "react";
import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  FlatList,
  ActivityIndicator,
} from "react-native";
import { router } from "expo-router";
import colors from "@/constants/colors";
import typography from "@/constants/typography";
import { useTranslation } from "@/hooks/useTranslation";
import { useCategories, useFeaturedProviders, useNearbyProviders } from "@/hooks/useApi";
import SearchBar from "@/components/SearchBar";
import ProviderCard from "@/components/ProviderCard";
import { MapPin, Gift } from "lucide-react-native";

export default function HomeScreen() {
  const { t } = useTranslation();
  const [searchQuery, setSearchQuery] = useState("");

  // Fetch data using API hooks
  const { data: categoriesData, loading: categoriesLoading } = useCategories();
  const { data: featuredData, loading: featuredLoading } = useFeaturedProviders();
  const { data: nearbyData, loading: nearbyLoading } = useNearbyProviders();

  const categories = categoriesData?.categories || [];
  const featuredProviders = featuredData?.providers || [];
  const nearbyProviders = nearbyData?.providers || [];

  const handleCategoryPress = (category: any) => {
    router.push(`/category/${category.id}`);
  };

  const handleProviderPress = (provider: any) => {
    router.push(`/provider/${provider.id}`);
  };

  const handleSearchSubmit = () => {
    if (searchQuery.trim()) {
      router.push(`/browse?search=${encodeURIComponent(searchQuery)}`);
    }
  };

  const handleSearchClear = () => {
    setSearchQuery("");
  };

  const renderCategoryItem = ({ item }: { item: any }) => (
    <TouchableOpacity
      style={styles.categoryCard}
      onPress={() => handleCategoryPress(item)}
      activeOpacity={0.7}
    >
      <View style={styles.categoryIcon}>
        <Text style={styles.categoryEmoji}>{item.icon}</Text>
      </View>
      <Text style={styles.categoryName}>{t(item.nameKey)}</Text>
    </TouchableOpacity>
  );

  const renderProviderItem = ({ item }: { item: any }) => (
    <ProviderCard provider={item} onPress={handleProviderPress} />
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.locationContainer}>
            <MapPin size={16} color={colors.textSecondary} />
            <Text style={styles.location}>{t("currentLocation")}</Text>
          </View>
          <Text style={styles.greeting}>{t("greeting")}</Text>
          <Text style={styles.subtitle}>{t("homeSubtitle")}</Text>
        </View>

        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <SearchBar
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholder={t("searchServicesOrProviders")}
            onSubmit={handleSearchSubmit}
            onClear={handleSearchClear}
          />
        </View>

        {/* Promotional Banner */}
        <TouchableOpacity style={styles.promoBanner} activeOpacity={0.8}>
          <View style={styles.promoContent}>
            <Gift size={24} color={colors.white} />
            <View style={styles.promoText}>
              <Text style={styles.promoTitle}>{t("promoTitle")}</Text>
              <Text style={styles.promoSubtitle}>{t("promoSubtitle")}</Text>
            </View>
          </View>
          <TouchableOpacity style={styles.promoButton}>
            <Text style={styles.promoButtonText}>{t("bookNow")}</Text>
          </TouchableOpacity>
        </TouchableOpacity>

        {/* Categories Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>{t("categories")}</Text>
            <TouchableOpacity onPress={() => router.push("/browse")}>
              <Text style={styles.seeAllText}>{t("seeAll")}</Text>
            </TouchableOpacity>
          </View>
          {categoriesLoading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="small" color={colors.primary} />
            </View>
          ) : (
            <FlatList
              data={categories}
              renderItem={renderCategoryItem}
              keyExtractor={(item) => item.id}
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.categoriesList}
            />
          )}
        </View>

        {/* Featured Providers Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>{t("featuredProviders")}</Text>
            <TouchableOpacity onPress={() => router.push("/browse")}>
              <Text style={styles.seeAllText}>{t("seeAll")}</Text>
            </TouchableOpacity>
          </View>
          {featuredLoading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="small" color={colors.primary} />
            </View>
          ) : (
            <FlatList
              data={featuredProviders}
              renderItem={renderProviderItem}
              keyExtractor={(item) => item.id}
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.providersList}
            />
          )}
        </View>

        {/* Nearby Providers Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>{t("nearbyProviders")}</Text>
            <TouchableOpacity onPress={() => router.push("/browse")}>
              <Text style={styles.seeAllText}>{t("seeAll")}</Text>
            </TouchableOpacity>
          </View>
          {nearbyLoading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="small" color={colors.primary} />
            </View>
          ) : (
            nearbyProviders.map((provider) => (
              <ProviderCard
                key={provider.id}
                provider={provider}
                onPress={handleProviderPress}
              />
            ))
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 16,
  },
  locationContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
    marginBottom: 8,
  },
  location: {
    ...typography.caption,
    color: colors.textSecondary,
  },
  greeting: {
    ...typography.h1,
    marginBottom: 4,
  },
  subtitle: {
    ...typography.body,
    color: colors.textSecondary,
  },
  searchContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  promoBanner: {
    backgroundColor: colors.primary,
    marginHorizontal: 20,
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  promoContent: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
    gap: 12,
  },
  promoText: {
    flex: 1,
  },
  promoTitle: {
    ...typography.h4,
    color: colors.white,
    marginBottom: 2,
  },
  promoSubtitle: {
    ...typography.bodySmall,
    color: colors.white,
    opacity: 0.9,
  },
  promoButton: {
    backgroundColor: colors.white,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  promoButtonText: {
    ...typography.bodySmall,
    color: colors.primary,
    fontWeight: "600",
  },
  section: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  sectionTitle: {
    ...typography.h3,
  },
  seeAllText: {
    ...typography.bodySmall,
    color: colors.primary,
    fontWeight: "500",
  },
  categoriesList: {
    paddingHorizontal: 12,
  },
  providersList: {
    paddingHorizontal: 20,
    gap: 16,
  },
  loadingContainer: {
    paddingVertical: 20,
    alignItems: "center",
  },
  categoryCard: {
    alignItems: "center",
    marginRight: 16,
    width: 80,
  },
  categoryIcon: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: colors.card,
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 8,
  },
  categoryEmoji: {
    fontSize: 24,
  },
  categoryName: {
    ...typography.caption,
    textAlign: "center",
    color: colors.text,
    fontWeight: "500",
  },
});