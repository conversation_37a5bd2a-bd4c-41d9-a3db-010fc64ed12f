import React, { useState } from "react";
import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
} from "react-native";
import { router } from "expo-router";
import colors from "@/constants/colors";
import typography from "@/constants/typography";
import { serviceCategories } from "@/mocks/categories";
import { serviceProviders } from "@/mocks/providers";

export default function HomeScreen() {
  const [searchQuery, setSearchQuery] = useState("");

  // Use mock data directly for now
  const categories = serviceCategories;
  const featuredProviders = serviceProviders.filter(provider => provider.featured);
  const nearbyProviders = serviceProviders
    .sort((a, b) => a.distance - b.distance)
    .slice(0, 5);

  const handleCategoryPress = (category: any) => {
    router.push(`/category/${category.id}`);
  };

  const handleProviderPress = (provider: any) => {
    router.push(`/provider/${provider.id}`);
  };

  const handleSearchSubmit = () => {
    if (searchQuery.trim()) {
      router.push(`/browse?search=${encodeURIComponent(searchQuery)}`);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.locationContainer}>
            <Text style={styles.location}>📍 Cape Town, South Africa</Text>
          </View>
          <Text style={styles.greeting}>Hello there!</Text>
          <Text style={styles.subtitle}>Find trusted service providers near you</Text>
        </View>

        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <View style={styles.searchBar}>
            <Text style={styles.searchIcon}>🔍</Text>
            <Text style={styles.searchPlaceholder}>What service do you need?</Text>
          </View>
        </View>

        {/* Promotional Banner */}
        <TouchableOpacity style={styles.promoBanner} activeOpacity={0.8}>
          <View style={styles.promoContent}>
            <Text style={styles.promoIcon}>🎁</Text>
            <View style={styles.promoText}>
              <Text style={styles.promoTitle}>Need a helping hand?</Text>
              <Text style={styles.promoSubtitle}>Get 15% off your first booking</Text>
            </View>
          </View>
          <TouchableOpacity style={styles.promoButton}>
            <Text style={styles.promoButtonText}>Book Now</Text>
          </TouchableOpacity>
        </TouchableOpacity>

        {/* Categories Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Categories</Text>
            <TouchableOpacity onPress={() => router.push("/browse")}>
              <Text style={styles.seeAllText}>See All</Text>
            </TouchableOpacity>
          </View>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.categoriesList}>
            {categories.slice(0, 4).map((category) => (
              <TouchableOpacity key={category.id} style={styles.categoryCard} onPress={() => handleCategoryPress(category)}>
                <View style={styles.categoryIcon}>
                  <Text style={styles.categoryEmoji}>{category.icon}</Text>
                </View>
                <Text style={styles.categoryName}>{category.nameKey}</Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* Featured Providers Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Featured Providers</Text>
            <TouchableOpacity onPress={() => router.push("/browse")}>
              <Text style={styles.seeAllText}>See All</Text>
            </TouchableOpacity>
          </View>
          {featuredProviders.slice(0, 2).map((provider) => (
            <TouchableOpacity key={provider.id} style={styles.providerCard} onPress={() => handleProviderPress(provider)}>
              <View style={styles.providerHeader}>
                <View style={styles.providerAvatar} />
                <View style={styles.providerInfo}>
                  <Text style={styles.providerName}>{provider.nameKey} {provider.verified ? '✓' : ''}</Text>
                  <Text style={styles.providerLocation}>📍 {provider.locationKey} • {provider.distance} km</Text>
                </View>
              </View>
              <Text style={styles.providerDescription}>{provider.descriptionKey}</Text>
              <View style={styles.providerFooter}>
                <Text style={styles.rating}>⭐ {provider.rating} ({provider.reviewCount})</Text>
                <View style={styles.priceContainer}>
                  <Text style={styles.priceLabel}>From</Text>
                  <Text style={styles.price}>R{Math.min(...provider.services.map(s => s.price))}</Text>
                </View>
              </View>
            </TouchableOpacity>
          ))}
        </View>

        {/* Nearby Providers Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Nearby Providers</Text>
            <TouchableOpacity onPress={() => router.push("/browse")}>
              <Text style={styles.seeAllText}>See All</Text>
            </TouchableOpacity>
          </View>
          {nearbyProviders.slice(0, 3).map((provider) => (
            <TouchableOpacity key={provider.id} style={styles.providerCard} onPress={() => handleProviderPress(provider)}>
              <View style={styles.providerHeader}>
                <View style={styles.providerAvatar} />
                <View style={styles.providerInfo}>
                  <Text style={styles.providerName}>{provider.nameKey} {provider.verified ? '✓' : ''}</Text>
                  <Text style={styles.providerLocation}>📍 {provider.locationKey} • {provider.distance} km</Text>
                </View>
              </View>
              <Text style={styles.providerDescription}>{provider.descriptionKey}</Text>
              <View style={styles.providerFooter}>
                <Text style={styles.rating}>⭐ {provider.rating} ({provider.reviewCount})</Text>
                <View style={styles.priceContainer}>
                  <Text style={styles.priceLabel}>From</Text>
                  <Text style={styles.price}>R{Math.min(...provider.services.map(s => s.price))}</Text>
                </View>
              </View>
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 16,
  },
  locationContainer: {
    marginBottom: 8,
  },
  location: {
    fontSize: 14,
    color: colors.textSecondary,
  },
  greeting: {
    fontSize: 28,
    fontWeight: "bold",
    marginBottom: 4,
    color: colors.text,
  },
  subtitle: {
    fontSize: 16,
    color: colors.textSecondary,
  },
  searchContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  searchBar: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: colors.card,
    borderRadius: 8,
    padding: 12,
    gap: 8,
  },
  searchIcon: {
    fontSize: 16,
  },
  searchPlaceholder: {
    fontSize: 16,
    color: colors.textSecondary,
    flex: 1,
  },
  promoBanner: {
    backgroundColor: colors.primary,
    marginHorizontal: 20,
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  promoContent: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
    gap: 12,
  },
  promoIcon: {
    fontSize: 24,
  },
  promoText: {
    flex: 1,
  },
  promoTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: colors.white,
    marginBottom: 2,
  },
  promoSubtitle: {
    fontSize: 14,
    color: colors.white,
    opacity: 0.9,
  },
  promoButton: {
    backgroundColor: colors.white,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  promoButtonText: {
    fontSize: 14,
    color: colors.primary,
    fontWeight: "600",
  },
  section: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: "600",
    color: colors.text,
  },
  seeAllText: {
    fontSize: 14,
    color: colors.primary,
    fontWeight: "500",
  },
  categoriesList: {
    paddingHorizontal: 20,
  },
  categoryCard: {
    alignItems: "center",
    marginRight: 16,
    width: 80,
  },
  categoryIcon: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: colors.card,
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 8,
  },
  categoryEmoji: {
    fontSize: 24,
  },
  categoryName: {
    fontSize: 12,
    textAlign: "center",
    color: colors.text,
    fontWeight: "500",
  },
  providerCard: {
    backgroundColor: colors.card,
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 20,
    marginBottom: 16,
  },
  providerHeader: {
    flexDirection: "row",
    marginBottom: 12,
  },
  providerAvatar: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: colors.background,
    marginRight: 12,
  },
  providerInfo: {
    flex: 1,
  },
  providerName: {
    fontSize: 18,
    fontWeight: "600",
    color: colors.text,
    marginBottom: 4,
  },
  providerLocation: {
    fontSize: 14,
    color: colors.textSecondary,
  },
  providerDescription: {
    fontSize: 14,
    color: colors.textSecondary,
    marginBottom: 12,
    lineHeight: 20,
  },
  providerFooter: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  rating: {
    fontSize: 14,
    fontWeight: "500",
    color: colors.text,
  },
  priceContainer: {
    alignItems: "flex-end",
  },
  priceLabel: {
    fontSize: 12,
    color: colors.textSecondary,
  },
  price: {
    fontSize: 16,
    fontWeight: "600",
    color: colors.primary,
  },
});