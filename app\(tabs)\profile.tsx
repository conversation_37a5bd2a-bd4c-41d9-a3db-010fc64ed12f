import React from "react";
import { StyleSheet, Text, View, SafeAreaView, ScrollView, TouchableOpacity, Alert } from "react-native";
import { router } from "expo-router";

export default function ProfileScreen() {
  const handleEditProfile = () => {
    Alert.alert("Edit Profile", "Update your profile information:", [
      { text: "Change Name", onPress: () => Alert.alert("Name", "Enter your new name") },
      { text: "Change Email", onPress: () => Alert.alert("Email", "Enter your new email") },
      { text: "Change Photo", onPress: () => Alert.alert("Photo", "Select a new profile photo") },
      { text: "Cancel", style: "cancel" },
    ]);
  };

  const handleMenuPress = (menuItem: string) => {
    switch (menuItem) {
      case "My Bookings":
        router.push("/bookings");
        break;
      case "Payment Methods":
        Alert.alert("Payment Methods", "Manage your payment options:", [
          { text: "Add Card", onPress: () => Alert.alert("Add Card", "Enter card details") },
          { text: "View Cards", onPress: () => Alert.alert("Cards", "Visa ****1234\nMasterCard ****5678") },
          { text: "Cancel", style: "cancel" },
        ]);
        break;
      case "Saved Addresses":
        Alert.alert("Saved Addresses", "Your saved locations:", [
          { text: "Add Address", onPress: () => Alert.alert("Add", "Enter new address") },
          { text: "View All", onPress: () => Alert.alert("Addresses", "Home: 123 Main St\nWork: 456 Oak Ave") },
          { text: "Cancel", style: "cancel" },
        ]);
        break;
      case "Notifications":
        Alert.alert("Notifications", "Notification settings:", [
          { text: "Push Notifications: ON", onPress: () => Alert.alert("Updated", "Push notifications disabled") },
          { text: "Email Notifications: ON", onPress: () => Alert.alert("Updated", "Email notifications disabled") },
          { text: "SMS Notifications: OFF", onPress: () => Alert.alert("Updated", "SMS notifications enabled") },
          { text: "Cancel", style: "cancel" },
        ]);
        break;
      case "Help & Support":
        Alert.alert("Help & Support", "How can we help you?", [
          { text: "FAQ", onPress: () => Alert.alert("FAQ", "Frequently asked questions...") },
          { text: "Contact Support", onPress: () => Alert.alert("Support", "Email: <EMAIL>\nPhone: +27 123 456 789") },
          { text: "Report Issue", onPress: () => Alert.alert("Report", "Describe your issue...") },
          { text: "Cancel", style: "cancel" },
        ]);
        break;
      case "Settings":
        Alert.alert("Settings", "App settings:", [
          { text: "Language: English", onPress: () => Alert.alert("Language", "Select language: English, Afrikaans, Zulu") },
          { text: "Theme: Light", onPress: () => Alert.alert("Theme", "Theme changed to Dark") },
          { text: "Privacy", onPress: () => Alert.alert("Privacy", "Privacy settings updated") },
          { text: "Cancel", style: "cancel" },
        ]);
        break;
      case "Logout":
        Alert.alert("Logout", "Are you sure you want to logout?", [
          { text: "Logout", onPress: () => Alert.alert("Logged Out", "You have been logged out successfully"), style: "destructive" },
          { text: "Cancel", style: "cancel" },
        ]);
        break;
    }
  };

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollView}>
        {/* Profile Header */}
        <View style={styles.profileHeader}>
          <View style={styles.profileAvatar} />
          <Text style={styles.profileName}>John Doe</Text>
          <Text style={styles.profileEmail}><EMAIL></Text>
          <TouchableOpacity style={styles.editButton} onPress={handleEditProfile} activeOpacity={0.8}>
            <Text style={styles.editButtonText}>Edit Profile</Text>
          </TouchableOpacity>
        </View>

        {/* Stats Section */}
        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>12</Text>
            <Text style={styles.statLabel}>Bookings</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>4.8</Text>
            <Text style={styles.statLabel}>Rating</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>R2,450</Text>
            <Text style={styles.statLabel}>Saved</Text>
          </View>
        </View>

        {/* Menu Items */}
        <View style={styles.menuContainer}>
          <TouchableOpacity style={styles.menuItem} onPress={() => handleMenuPress("My Bookings")} activeOpacity={0.7}>
            <Text style={styles.menuIcon}>📋</Text>
            <Text style={styles.menuText}>My Bookings</Text>
            <Text style={styles.menuArrow}>›</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.menuItem} onPress={() => handleMenuPress("Payment Methods")} activeOpacity={0.7}>
            <Text style={styles.menuIcon}>💳</Text>
            <Text style={styles.menuText}>Payment Methods</Text>
            <Text style={styles.menuArrow}>›</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.menuItem} onPress={() => handleMenuPress("Saved Addresses")} activeOpacity={0.7}>
            <Text style={styles.menuIcon}>📍</Text>
            <Text style={styles.menuText}>Saved Addresses</Text>
            <Text style={styles.menuArrow}>›</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.menuItem} onPress={() => handleMenuPress("Notifications")} activeOpacity={0.7}>
            <Text style={styles.menuIcon}>🔔</Text>
            <Text style={styles.menuText}>Notifications</Text>
            <Text style={styles.menuArrow}>›</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.menuItem} onPress={() => handleMenuPress("Help & Support")} activeOpacity={0.7}>
            <Text style={styles.menuIcon}>❓</Text>
            <Text style={styles.menuText}>Help & Support</Text>
            <Text style={styles.menuArrow}>›</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.menuItem} onPress={() => handleMenuPress("Settings")} activeOpacity={0.7}>
            <Text style={styles.menuIcon}>⚙️</Text>
            <Text style={styles.menuText}>Settings</Text>
            <Text style={styles.menuArrow}>›</Text>
          </TouchableOpacity>

          <TouchableOpacity style={[styles.menuItem, styles.logoutItem]} onPress={() => handleMenuPress("Logout")} activeOpacity={0.7}>
            <Text style={styles.menuIcon}>🚪</Text>
            <Text style={[styles.menuText, styles.logoutText]}>Logout</Text>
            <Text style={styles.menuArrow}>›</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F9FAFB",
  },
  scrollView: {
    flex: 1,
  },
  profileHeader: {
    alignItems: "center",
    paddingHorizontal: 20,
    paddingTop: 60, // Increased for full-screen iPhone
    paddingBottom: 20,
    backgroundColor: "#FFFFFF",
    marginBottom: 20,
  },
  profileAvatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: "#F3F4F6",
    marginBottom: 16,
  },
  profileName: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#1F2937",
    marginBottom: 4,
  },
  profileEmail: {
    fontSize: 16,
    color: "#6B7280",
    marginBottom: 16,
  },
  editButton: {
    backgroundColor: "#10B981",
    paddingHorizontal: 20,
    paddingVertical: 8,
    borderRadius: 8,
  },
  editButtonText: {
    color: "#FFFFFF",
    fontSize: 14,
    fontWeight: "600",
  },
  statsContainer: {
    flexDirection: "row",
    backgroundColor: "#FFFFFF",
    marginHorizontal: 20,
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statItem: {
    flex: 1,
    alignItems: "center",
  },
  statNumber: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#1F2937",
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 14,
    color: "#6B7280",
  },
  menuContainer: {
    backgroundColor: "#FFFFFF",
    marginHorizontal: 20,
    borderRadius: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  menuItem: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#F3F4F6",
  },
  menuIcon: {
    fontSize: 20,
    marginRight: 12,
    width: 24,
  },
  menuText: {
    flex: 1,
    fontSize: 16,
    color: "#1F2937",
  },
  menuArrow: {
    fontSize: 18,
    color: "#6B7280",
  },
  logoutItem: {
    borderBottomWidth: 0,
  },
  logoutText: {
    color: "#EF4444",
  },
});