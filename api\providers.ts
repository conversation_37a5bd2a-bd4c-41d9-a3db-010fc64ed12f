// Providers API endpoints
import { serviceProviders, ServiceProvider } from "@/mocks/providers";
import { serviceCategories } from "@/mocks/categories";

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface ProvidersResponse {
  providers: ServiceProvider[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

export interface ProvidersFilters {
  categoryId?: string;
  search?: string;
  minRating?: number;
  maxDistance?: number;
  featured?: boolean;
  verified?: boolean;
  sortBy?: 'rating' | 'distance' | 'price' | 'name';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

/**
 * Get all service providers with optional filters
 */
export async function getProviders(filters: ProvidersFilters = {}): Promise<ApiResponse<ProvidersResponse>> {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 600));
    
    let filteredProviders = [...serviceProviders];
    
    // Apply filters
    if (filters.categoryId) {
      filteredProviders = filteredProviders.filter(provider => 
        provider.categoryId === filters.categoryId
      );
    }
    
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase().trim();
      filteredProviders = filteredProviders.filter(provider => 
        provider.nameKey.toLowerCase().includes(searchTerm) ||
        provider.descriptionKey.toLowerCase().includes(searchTerm) ||
        provider.services.some(service => 
          service.nameKey.toLowerCase().includes(searchTerm)
        )
      );
    }
    
    if (filters.minRating) {
      filteredProviders = filteredProviders.filter(provider => 
        provider.rating >= filters.minRating!
      );
    }
    
    if (filters.maxDistance) {
      filteredProviders = filteredProviders.filter(provider => 
        provider.distance <= filters.maxDistance!
      );
    }
    
    if (filters.featured !== undefined) {
      filteredProviders = filteredProviders.filter(provider => 
        provider.featured === filters.featured
      );
    }
    
    if (filters.verified !== undefined) {
      filteredProviders = filteredProviders.filter(provider => 
        provider.verified === filters.verified
      );
    }
    
    // Apply sorting
    if (filters.sortBy) {
      filteredProviders.sort((a, b) => {
        let aValue: any, bValue: any;
        
        switch (filters.sortBy) {
          case 'rating':
            aValue = a.rating;
            bValue = b.rating;
            break;
          case 'distance':
            aValue = a.distance;
            bValue = b.distance;
            break;
          case 'price':
            aValue = Math.min(...a.services.map(s => s.price));
            bValue = Math.min(...b.services.map(s => s.price));
            break;
          case 'name':
            aValue = a.nameKey;
            bValue = b.nameKey;
            break;
          default:
            return 0;
        }
        
        if (filters.sortOrder === 'desc') {
          return bValue > aValue ? 1 : bValue < aValue ? -1 : 0;
        } else {
          return aValue > bValue ? 1 : aValue < bValue ? -1 : 0;
        }
      });
    }
    
    // Apply pagination
    const page = filters.page || 1;
    const limit = filters.limit || 10;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedProviders = filteredProviders.slice(startIndex, endIndex);
    
    return {
      success: true,
      data: {
        providers: paginatedProviders,
        total: filteredProviders.length,
        page,
        limit,
        hasMore: endIndex < filteredProviders.length
      }
    };
  } catch (error) {
    return {
      success: false,
      error: "Failed to fetch providers",
      message: "An error occurred while fetching service providers"
    };
  }
}

/**
 * Get a specific provider by ID
 */
export async function getProviderById(id: string): Promise<ApiResponse<ServiceProvider>> {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300));
    
    const provider = serviceProviders.find(p => p.id === id);
    
    if (!provider) {
      return {
        success: false,
        error: "Provider not found",
        message: `Provider with ID ${id} does not exist`
      };
    }
    
    return {
      success: true,
      data: provider
    };
  } catch (error) {
    return {
      success: false,
      error: "Failed to fetch provider",
      message: "An error occurred while fetching the provider"
    };
  }
}

/**
 * Get featured providers
 */
export async function getFeaturedProviders(): Promise<ApiResponse<ProvidersResponse>> {
  return getProviders({ featured: true, limit: 10 });
}

/**
 * Get nearby providers (sorted by distance)
 */
export async function getNearbyProviders(maxDistance: number = 10): Promise<ApiResponse<ProvidersResponse>> {
  return getProviders({ 
    maxDistance, 
    sortBy: 'distance', 
    sortOrder: 'asc',
    limit: 10 
  });
}

/**
 * Get providers by category
 */
export async function getProvidersByCategory(categoryId: string): Promise<ApiResponse<ProvidersResponse>> {
  return getProviders({ categoryId });
}
