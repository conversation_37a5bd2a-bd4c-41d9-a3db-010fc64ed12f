// API exports - centralized access to all API endpoints
export * from './auth';
export * from './categories';
export * from './providers';
export * from './search';

// Re-export common types
export type { ApiResponse } from './auth';

// API configuration
export const API_CONFIG = {
  baseUrl: process.env.EXPO_PUBLIC_API_URL || 'http://localhost:3000/api',
  timeout: 10000,
  retryAttempts: 3,
  retryDelay: 1000,
};

// API client configuration for future HTTP requests
export const apiClient = {
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
  timeout: API_CONFIG.timeout,
};

// Error handling utilities
export class ApiError extends Error {
  constructor(
    message: string,
    public statusCode?: number,
    public errorCode?: string
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

// Response wrapper for consistent error handling
export function handleApiResponse<T>(response: any): T {
  if (!response.success) {
    throw new ApiError(
      response.message || response.error || 'An error occurred',
      response.statusCode,
      response.errorCode
    );
  }
  return response.data;
}

// Utility function for making HTTP requests (for future use)
export async function makeRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  const url = `${API_CONFIG.baseUrl}${endpoint}`;
  
  const config: RequestInit = {
    ...options,
    headers: {
      ...apiClient.headers,
      ...options.headers,
    },
  };

  try {
    const response = await fetch(url, config);
    const data = await response.json();
    
    if (!response.ok) {
      throw new ApiError(
        data.message || 'Request failed',
        response.status,
        data.errorCode
      );
    }
    
    return handleApiResponse<T>(data);
  } catch (error) {
    if (error instanceof ApiError) {
      throw error;
    }
    throw new ApiError('Network error occurred');
  }
}
