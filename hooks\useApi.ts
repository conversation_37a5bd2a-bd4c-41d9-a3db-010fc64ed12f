// Custom hooks for API interactions
import { useState, useEffect, useCallback } from 'react';
import { 
  getCategories, 
  getProviders, 
  search, 
  getFeaturedProviders,
  getNearbyProviders,
  getProvidersByCategory,
  ProvidersFilters,
  SearchFilters,
  ApiResponse 
} from '@/api';

// Generic hook for API calls
export function useApi<T>(
  apiCall: () => Promise<ApiResponse<T>>,
  dependencies: any[] = []
) {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiCall();
      
      if (response.success && response.data) {
        setData(response.data);
      } else {
        setError(response.error || 'An error occurred');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  }, dependencies);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return { data, loading, error, refetch: fetchData };
}

// Hook for categories
export function useCategories() {
  return useApi(() => getCategories());
}

// Hook for providers with filters
export function useProviders(filters: ProvidersFilters = {}) {
  return useApi(() => getProviders(filters), [
    filters.categoryId,
    filters.search,
    filters.minRating,
    filters.maxDistance,
    filters.featured,
    filters.verified,
    filters.sortBy,
    filters.sortOrder,
    filters.page,
    filters.limit
  ]);
}

// Hook for featured providers
export function useFeaturedProviders() {
  return useApi(() => getFeaturedProviders());
}

// Hook for nearby providers
export function useNearbyProviders(maxDistance?: number) {
  return useApi(() => getNearbyProviders(maxDistance), [maxDistance]);
}

// Hook for providers by category
export function useProvidersByCategory(categoryId: string) {
  return useApi(() => getProvidersByCategory(categoryId), [categoryId]);
}

// Hook for search functionality
export function useSearch(filters: SearchFilters) {
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const performSearch = useCallback(async (searchFilters: SearchFilters) => {
    if (!searchFilters.query.trim()) {
      setData(null);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      const response = await search(searchFilters);
      
      if (response.success && response.data) {
        setData(response.data);
      } else {
        setError(response.error || 'Search failed');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Search failed');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    if (filters.query) {
      performSearch(filters);
    }
  }, [filters.query, filters.type, filters.categoryId, performSearch]);

  return { data, loading, error, search: performSearch };
}

// Hook for debounced search
export function useDebouncedSearch(query: string, delay: number = 500) {
  const [debouncedQuery, setDebouncedQuery] = useState(query);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedQuery(query);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [query, delay]);

  return useSearch({ query: debouncedQuery });
}

// Hook for infinite scroll/pagination
export function useInfiniteProviders(filters: ProvidersFilters = {}) {
  const [allProviders, setAllProviders] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(1);

  const loadMore = useCallback(async () => {
    if (loading || !hasMore) return;

    try {
      setLoading(true);
      setError(null);
      
      const response = await getProviders({
        ...filters,
        page,
        limit: 10
      });
      
      if (response.success && response.data) {
        const { providers, hasMore: moreAvailable } = response.data;
        
        if (page === 1) {
          setAllProviders(providers);
        } else {
          setAllProviders(prev => [...prev, ...providers]);
        }
        
        setHasMore(moreAvailable);
        setPage(prev => prev + 1);
      } else {
        setError(response.error || 'Failed to load providers');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load providers');
    } finally {
      setLoading(false);
    }
  }, [filters, page, loading, hasMore]);

  const reset = useCallback(() => {
    setAllProviders([]);
    setPage(1);
    setHasMore(true);
    setError(null);
  }, []);

  useEffect(() => {
    reset();
  }, [
    filters.categoryId,
    filters.search,
    filters.minRating,
    filters.maxDistance,
    filters.featured,
    filters.verified,
    filters.sortBy,
    filters.sortOrder,
    reset
  ]);

  useEffect(() => {
    if (page === 1) {
      loadMore();
    }
  }, [page]);

  return {
    providers: allProviders,
    loading,
    error,
    hasMore,
    loadMore,
    reset
  };
}
