@echo off
echo ========================================
echo    TaskhubSA - Expo Development Server
echo ========================================
echo.

echo Checking if Node.js is installed...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed!
    echo.
    echo Please install Node.js from: https://nodejs.org/
    echo Then run this script again.
    echo.
    pause
    exit /b 1
)

echo ✅ Node.js is installed!
echo.

echo Checking if dependencies are installed...
if not exist "node_modules" (
    echo 📦 Installing dependencies...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ Failed to install dependencies!
        pause
        exit /b 1
    )
    echo ✅ Dependencies installed successfully!
    echo.
)

echo 🚀 Starting Expo development server...
echo.
echo Choose an option:
echo [1] Start for mobile (QR code)
echo [2] Start for web browser
echo [3] Start with tunnel (for remote testing)
echo.
set /p choice="Enter your choice (1-3): "

if "%choice%"=="1" (
    echo Starting Expo for mobile...
    expo start
) else if "%choice%"=="2" (
    echo Starting Expo for web...
    expo start --web
) else if "%choice%"=="3" (
    echo Starting Expo with tunnel...
    expo start --tunnel
) else (
    echo Invalid choice. Starting default mode...
    expo start
)

pause
