// Categories API endpoints
import { serviceCategories, ServiceCategory } from "@/mocks/categories";

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface CategoriesResponse {
  categories: ServiceCategory[];
  total: number;
}

/**
 * Get all service categories
 */
export async function getCategories(): Promise<ApiResponse<CategoriesResponse>> {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    return {
      success: true,
      data: {
        categories: serviceCategories,
        total: serviceCategories.length
      }
    };
  } catch (error) {
    return {
      success: false,
      error: "Failed to fetch categories",
      message: "An error occurred while fetching service categories"
    };
  }
}

/**
 * Get a specific category by ID
 */
export async function getCategoryById(id: string): Promise<ApiResponse<ServiceCategory>> {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300));
    
    const category = serviceCategories.find(cat => cat.id === id);
    
    if (!category) {
      return {
        success: false,
        error: "Category not found",
        message: `Category with ID ${id} does not exist`
      };
    }
    
    return {
      success: true,
      data: category
    };
  } catch (error) {
    return {
      success: false,
      error: "Failed to fetch category",
      message: "An error occurred while fetching the category"
    };
  }
}

/**
 * Search categories by name or description
 */
export async function searchCategories(query: string): Promise<ApiResponse<CategoriesResponse>> {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 400));
    
    const searchTerm = query.toLowerCase().trim();
    
    if (!searchTerm) {
      return getCategories();
    }
    
    const filteredCategories = serviceCategories.filter(category => 
      category.nameKey.toLowerCase().includes(searchTerm) ||
      category.descriptionKey.toLowerCase().includes(searchTerm) ||
      category.popularServicesKeys.some(service => 
        service.toLowerCase().includes(searchTerm)
      )
    );
    
    return {
      success: true,
      data: {
        categories: filteredCategories,
        total: filteredCategories.length
      }
    };
  } catch (error) {
    return {
      success: false,
      error: "Failed to search categories",
      message: "An error occurred while searching categories"
    };
  }
}
