import React from "react";
import { StyleSheet, Text, View, SafeAreaView, ScrollView, TouchableOpacity } from "react-native";

export default function BookingsScreen() {
  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        <View style={styles.header}>
          <Text style={styles.title}>My Bookings</Text>
        </View>

        {/* Booking Cards */}
        <View style={styles.content}>
          <TouchableOpacity style={styles.bookingCard}>
            <View style={styles.bookingHeader}>
              <View style={styles.statusBadge}>
                <Text style={styles.statusText}>Confirmed</Text>
              </View>
              <Text style={styles.bookingDate}>Today, 2:00 PM</Text>
            </View>
            <Text style={styles.serviceName}>House Cleaning</Text>
            <Text style={styles.providerName}>Thabo's Cleaning Services</Text>
            <Text style={styles.bookingLocation}>📍 123 Main St, Cape Town</Text>
            <View style={styles.bookingFooter}>
              <Text style={styles.bookingPrice}>R350</Text>
              <TouchableOpacity style={styles.actionButton}>
                <Text style={styles.actionButtonText}>View Details</Text>
              </TouchableOpacity>
            </View>
          </TouchableOpacity>

          <TouchableOpacity style={styles.bookingCard}>
            <View style={styles.bookingHeader}>
              <View style={[styles.statusBadge, styles.pendingBadge]}>
                <Text style={styles.statusText}>Pending</Text>
              </View>
              <Text style={styles.bookingDate}>Tomorrow, 10:00 AM</Text>
            </View>
            <Text style={styles.serviceName}>Electrical Repair</Text>
            <Text style={styles.providerName}>Sipho's Electrical</Text>
            <Text style={styles.bookingLocation}>📍 456 Oak Ave, Durban</Text>
            <View style={styles.bookingFooter}>
              <Text style={styles.bookingPrice}>R450</Text>
              <TouchableOpacity style={styles.actionButton}>
                <Text style={styles.actionButtonText}>View Details</Text>
              </TouchableOpacity>
            </View>
          </TouchableOpacity>

          <TouchableOpacity style={styles.bookingCard}>
            <View style={styles.bookingHeader}>
              <View style={[styles.statusBadge, styles.completedBadge]}>
                <Text style={styles.statusText}>Completed</Text>
              </View>
              <Text style={styles.bookingDate}>Yesterday, 3:00 PM</Text>
            </View>
            <Text style={styles.serviceName}>Hair Styling</Text>
            <Text style={styles.providerName}>Lerato's Beauty</Text>
            <Text style={styles.bookingLocation}>📍 789 Pine St, Pretoria</Text>
            <View style={styles.bookingFooter}>
              <Text style={styles.bookingPrice}>R280</Text>
              <TouchableOpacity style={[styles.actionButton, styles.rateButton]}>
                <Text style={styles.actionButtonText}>Rate Service</Text>
              </TouchableOpacity>
            </View>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F9FAFB",
  },
  scrollView: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 16,
  },
  title: {
    fontSize: 28,
    fontWeight: "bold",
    color: "#1F2937",
  },
  content: {
    paddingHorizontal: 20,
  },
  bookingCard: {
    backgroundColor: "#FFFFFF",
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  bookingHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  statusBadge: {
    backgroundColor: "#10B981",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  pendingBadge: {
    backgroundColor: "#F59E0B",
  },
  completedBadge: {
    backgroundColor: "#6B7280",
  },
  statusText: {
    color: "#FFFFFF",
    fontSize: 12,
    fontWeight: "600",
  },
  bookingDate: {
    fontSize: 14,
    color: "#6B7280",
    fontWeight: "500",
  },
  serviceName: {
    fontSize: 18,
    fontWeight: "600",
    color: "#1F2937",
    marginBottom: 4,
  },
  providerName: {
    fontSize: 16,
    color: "#10B981",
    fontWeight: "500",
    marginBottom: 8,
  },
  bookingLocation: {
    fontSize: 14,
    color: "#6B7280",
    marginBottom: 12,
  },
  bookingFooter: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  bookingPrice: {
    fontSize: 18,
    fontWeight: "600",
    color: "#1F2937",
  },
  actionButton: {
    backgroundColor: "#10B981",
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  rateButton: {
    backgroundColor: "#F59E0B",
  },
  actionButtonText: {
    color: "#FFFFFF",
    fontSize: 14,
    fontWeight: "600",
  },
});