import { Stack } from "expo-router";
import * as SplashScreen from "expo-splash-screen";
import { useEffect } from "react";
import { Text, View } from "react-native";

export const unstable_settings = {
  initialRouteName: "(tabs)",
};

// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  useEffect(() => {
    // Force hide splash screen immediately
    SplashScreen.hideAsync().catch((error) => {
      console.log("Error hiding splash screen:", error);
    });
  }, []);

  try {
    return <RootLayoutNav />;
  } catch (error) {
    console.error("Layout error:", error);
    // Force hide splash screen even on error
    SplashScreen.hideAsync().catch(console.error);
    return (
      <View style={{ flex: 1, justifyContent: "center", alignItems: "center", backgroundColor: "#F9FAFB" }}>
        <Text style={{ color: "#1F2937", fontSize: 16, textAlign: "center", padding: 20 }}>
          Error loading app: {String(error)}
        </Text>
      </View>
    );
  }
}

function RootLayoutNav() {
  return (
    <Stack
      screenOptions={{
        headerStyle: {
          backgroundColor: "#F9FAFB",
        },
        headerTintColor: "#10B981",
        headerTitleStyle: {
          fontWeight: "600",
        },
        headerShadowVisible: false,
      }}
    >
      <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
    </Stack>
  );
}