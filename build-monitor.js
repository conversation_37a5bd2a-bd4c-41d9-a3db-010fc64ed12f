#!/usr/bin/env node

const { spawn } = require('child_process');
const readline = require('readline');

console.log('🚀 TaskhubSA Build Monitor');
console.log('========================\n');

let webProgress = 0;
let iosProgress = 0;
let webModules = 0;
let iosModules = 0;
let webTotal = 0;
let iosTotal = 0;

function updateProgress() {
  process.stdout.write('\r\x1b[K'); // Clear line
  
  const webBar = '█'.repeat(Math.floor(webProgress / 5)) + '░'.repeat(20 - Math.floor(webProgress / 5));
  const iosBar = '█'.repeat(Math.floor(iosProgress / 5)) + '░'.repeat(20 - Math.floor(iosProgress / 5));
  
  process.stdout.write(
    `📱 iOS:  [${iosBar}] ${iosProgress.toFixed(1)}% (${iosModules}/${iosTotal} modules)\n` +
    `🌐 Web:  [${webBar}] ${webProgress.toFixed(1)}% (${webModules}/${webTotal} modules)\n` +
    `\x1b[2A` // Move cursor up 2 lines
  );
}

// Start expo
const expo = spawn('npx', ['expo', 'start'], {
  stdio: ['inherit', 'pipe', 'pipe'],
  shell: true
});

const rl = readline.createInterface({
  input: expo.stdout,
  crlfDelay: Infinity
});

rl.on('line', (line) => {
  // Parse iOS progress
  const iosMatch = line.match(/iOS.*?(\d+\.\d+)%.*?\((\d+)\/(\d+)\)/);
  if (iosMatch) {
    iosProgress = parseFloat(iosMatch[1]);
    iosModules = parseInt(iosMatch[2]);
    iosTotal = parseInt(iosMatch[3]);
    updateProgress();
  }
  
  // Parse Web progress
  const webMatch = line.match(/Web.*?(\d+\.\d+)%.*?\((\d+)\/(\d+)\)/);
  if (webMatch) {
    webProgress = parseFloat(webMatch[1]);
    webModules = parseInt(webMatch[2]);
    webTotal = parseInt(webMatch[3]);
    updateProgress();
  }
  
  // Check for completion
  if (line.includes('Bundled') && line.includes('ms')) {
    if (line.includes('iOS')) {
      console.log(`\n✅ iOS Bundle Complete!`);
    }
    if (line.includes('Web')) {
      console.log(`\n✅ Web Bundle Complete!`);
    }
  }
  
  // Check for errors
  if (line.includes('ERROR') || line.includes('Error') || line.includes('Failed')) {
    console.log(`\n❌ Error: ${line}`);
  }
  
  // Show QR code info
  if (line.includes('Metro waiting on')) {
    console.log(`\n📱 Scan QR code with Expo Go app`);
  }
  
  if (line.includes('Web is waiting on')) {
    console.log(`\n🌐 Web app: http://localhost:8081`);
  }
});

expo.stderr.on('data', (data) => {
  console.log(`\n❌ Error: ${data.toString()}`);
});

expo.on('close', (code) => {
  console.log(`\n🏁 Expo process exited with code ${code}`);
});

// Handle Ctrl+C
process.on('SIGINT', () => {
  console.log('\n👋 Stopping build monitor...');
  expo.kill();
  process.exit();
});
