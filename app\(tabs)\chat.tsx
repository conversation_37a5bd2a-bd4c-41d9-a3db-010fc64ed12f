import React from "react";
import { View, Text, StyleSheet, SafeAreaView, ScrollView, TouchableOpacity, Alert } from "react-native";

export default function ChatTabScreen() {
  const handleChatPress = (providerName: string, hasUnread: boolean) => {
    if (hasUnread) {
      Alert.alert("New Messages", `You have unread messages from ${providerName}`, [
        { text: "Open Chat", onPress: () => openChat(providerName) },
        { text: "Mark as Read", onPress: () => Alert.alert("Marked", "Messages marked as read") },
        { text: "Cancel", style: "cancel" },
      ]);
    } else {
      openChat(providerName);
    }
  };

  const openChat = (providerName: string) => {
    Alert.alert("Chat", `Opening chat with ${providerName}`, [
      { text: "Send Message", onPress: () => Alert.alert("Message", "Type your message...") },
      { text: "Call Provider", onPress: () => Alert.alert("Calling", `Calling ${providerName}...`) },
      { text: "View Profile", onPress: () => Alert.alert("Profile", `Viewing ${providerName} profile`) },
      { text: "Close", style: "cancel" },
    ]);
  };

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollView}>
        <View style={styles.header}>
          <Text style={styles.title}>Messages</Text>
        </View>

        <View style={styles.content}>
          {/* Chat Conversations */}
          <TouchableOpacity style={styles.chatItem} onPress={() => handleChatPress("Thabo's Cleaning Services", true)} activeOpacity={0.8}>
            <View style={styles.avatar} />
            <View style={styles.chatContent}>
              <View style={styles.chatHeader}>
                <Text style={styles.chatName}>Thabo's Cleaning Services</Text>
                <Text style={styles.chatTime}>2:30 PM</Text>
              </View>
              <Text style={styles.lastMessage}>Thanks for booking! I'll be there at 2 PM tomorrow.</Text>
              <View style={styles.unreadBadge}>
                <Text style={styles.unreadCount}>2</Text>
              </View>
            </View>
          </TouchableOpacity>

          <TouchableOpacity style={styles.chatItem} onPress={() => handleChatPress("Sipho's Electrical", false)} activeOpacity={0.8}>
            <View style={styles.avatar} />
            <View style={styles.chatContent}>
              <View style={styles.chatHeader}>
                <Text style={styles.chatName}>Sipho's Electrical</Text>
                <Text style={styles.chatTime}>Yesterday</Text>
              </View>
              <Text style={styles.lastMessage}>The electrical work has been completed successfully.</Text>
            </View>
          </TouchableOpacity>

          <TouchableOpacity style={styles.chatItem} onPress={() => handleChatPress("Lerato's Beauty", false)} activeOpacity={0.8}>
            <View style={styles.avatar} />
            <View style={styles.chatContent}>
              <View style={styles.chatHeader}>
                <Text style={styles.chatName}>Lerato's Beauty</Text>
                <Text style={styles.chatTime}>Monday</Text>
              </View>
              <Text style={styles.lastMessage}>Thank you for the 5-star review! 😊</Text>
            </View>
          </TouchableOpacity>

          <TouchableOpacity style={styles.chatItem} onPress={() => handleChatPress("TaskhubSA Support", true)} activeOpacity={0.8}>
            <View style={styles.avatar} />
            <View style={styles.chatContent}>
              <View style={styles.chatHeader}>
                <Text style={styles.chatName}>TaskhubSA Support</Text>
                <Text style={styles.chatTime}>Last week</Text>
              </View>
              <Text style={styles.lastMessage}>Welcome to TaskhubSA! How can we help you today?</Text>
              <View style={styles.unreadBadge}>
                <Text style={styles.unreadCount}>1</Text>
              </View>
            </View>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F9FAFB",
  },
  scrollView: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 60, // Increased for full-screen iPhone
    paddingBottom: 16,
  },
  title: {
    fontSize: 28,
    fontWeight: "bold",
    color: "#1F2937",
  },
  content: {
    paddingHorizontal: 20,
  },
  chatItem: {
    flexDirection: "row",
    backgroundColor: "#FFFFFF",
    padding: 16,
    marginBottom: 12,
    borderRadius: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: "#F3F4F6",
    marginRight: 12,
  },
  chatContent: {
    flex: 1,
    position: "relative",
  },
  chatHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 4,
  },
  chatName: {
    fontSize: 16,
    fontWeight: "600",
    color: "#1F2937",
  },
  chatTime: {
    fontSize: 12,
    color: "#6B7280",
  },
  lastMessage: {
    fontSize: 14,
    color: "#6B7280",
    lineHeight: 20,
  },
  unreadBadge: {
    position: "absolute",
    top: 0,
    right: 0,
    backgroundColor: "#10B981",
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  unreadCount: {
    color: "#FFFFFF",
    fontSize: 12,
    fontWeight: "600",
  },
});