// Search API endpoints
import { serviceProviders, ServiceProvider } from "@/mocks/providers";
import { serviceCategories, ServiceCategory } from "@/mocks/categories";

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface SearchResult {
  type: 'provider' | 'category' | 'service';
  id: string;
  title: string;
  subtitle?: string;
  description?: string;
  categoryId?: string;
  providerId?: string;
  rating?: number;
  distance?: number;
  price?: number;
}

export interface SearchResponse {
  results: SearchResult[];
  total: number;
  query: string;
  suggestions: string[];
}

export interface SearchFilters {
  query: string;
  type?: 'all' | 'providers' | 'categories' | 'services';
  categoryId?: string;
  maxResults?: number;
}

/**
 * Perform a comprehensive search across providers, categories, and services
 */
export async function search(filters: SearchFilters): Promise<ApiResponse<SearchResponse>> {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const { query, type = 'all', categoryId, maxResults = 20 } = filters;
    const searchTerm = query.toLowerCase().trim();
    
    if (!searchTerm) {
      return {
        success: true,
        data: {
          results: [],
          total: 0,
          query,
          suggestions: []
        }
      };
    }
    
    let results: SearchResult[] = [];
    
    // Search providers
    if (type === 'all' || type === 'providers') {
      const providerResults = serviceProviders
        .filter(provider => {
          if (categoryId && provider.categoryId !== categoryId) return false;
          
          return (
            provider.nameKey.toLowerCase().includes(searchTerm) ||
            provider.descriptionKey.toLowerCase().includes(searchTerm) ||
            provider.locationKey.toLowerCase().includes(searchTerm)
          );
        })
        .map(provider => ({
          type: 'provider' as const,
          id: provider.id,
          title: provider.nameKey,
          subtitle: provider.locationKey,
          description: provider.descriptionKey,
          categoryId: provider.categoryId,
          rating: provider.rating,
          distance: provider.distance,
          price: Math.min(...provider.services.map(s => s.price))
        }));
      
      results.push(...providerResults);
    }
    
    // Search categories
    if (type === 'all' || type === 'categories') {
      const categoryResults = serviceCategories
        .filter(category => 
          category.nameKey.toLowerCase().includes(searchTerm) ||
          category.descriptionKey.toLowerCase().includes(searchTerm) ||
          category.popularServicesKeys.some(service => 
            service.toLowerCase().includes(searchTerm)
          )
        )
        .map(category => ({
          type: 'category' as const,
          id: category.id,
          title: category.nameKey,
          description: category.descriptionKey
        }));
      
      results.push(...categoryResults);
    }
    
    // Search services
    if (type === 'all' || type === 'services') {
      const serviceResults: SearchResult[] = [];
      
      serviceProviders.forEach(provider => {
        if (categoryId && provider.categoryId !== categoryId) return;
        
        provider.services.forEach(service => {
          if (
            service.nameKey.toLowerCase().includes(searchTerm) ||
            service.descriptionKey.toLowerCase().includes(searchTerm)
          ) {
            serviceResults.push({
              type: 'service',
              id: service.id,
              title: service.nameKey,
              subtitle: provider.nameKey,
              description: service.descriptionKey,
              categoryId: provider.categoryId,
              providerId: provider.id,
              rating: provider.rating,
              distance: provider.distance,
              price: service.price
            });
          }
        });
      });
      
      results.push(...serviceResults);
    }
    
    // Sort results by relevance (exact matches first, then partial matches)
    results.sort((a, b) => {
      const aExactMatch = a.title.toLowerCase() === searchTerm;
      const bExactMatch = b.title.toLowerCase() === searchTerm;
      
      if (aExactMatch && !bExactMatch) return -1;
      if (!aExactMatch && bExactMatch) return 1;
      
      // Sort by rating for providers and services
      if (a.rating && b.rating) {
        return b.rating - a.rating;
      }
      
      return 0;
    });
    
    // Limit results
    if (maxResults > 0) {
      results = results.slice(0, maxResults);
    }
    
    // Generate search suggestions
    const suggestions = generateSearchSuggestions(searchTerm);
    
    return {
      success: true,
      data: {
        results,
        total: results.length,
        query,
        suggestions
      }
    };
  } catch (error) {
    return {
      success: false,
      error: "Search failed",
      message: "An error occurred while performing the search"
    };
  }
}

/**
 * Get search suggestions based on query
 */
export async function getSearchSuggestions(query: string): Promise<ApiResponse<string[]>> {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 200));
    
    const suggestions = generateSearchSuggestions(query);
    
    return {
      success: true,
      data: suggestions
    };
  } catch (error) {
    return {
      success: false,
      error: "Failed to get suggestions",
      message: "An error occurred while fetching search suggestions"
    };
  }
}

/**
 * Generate search suggestions based on available data
 */
function generateSearchSuggestions(query: string): string[] {
  const searchTerm = query.toLowerCase().trim();
  
  if (!searchTerm) return [];
  
  const suggestions: string[] = [];
  
  // Add category suggestions
  serviceCategories.forEach(category => {
    if (category.nameKey.toLowerCase().includes(searchTerm)) {
      suggestions.push(category.nameKey);
    }
    
    category.popularServicesKeys.forEach(service => {
      if (service.toLowerCase().includes(searchTerm)) {
        suggestions.push(service);
      }
    });
  });
  
  // Add provider suggestions
  serviceProviders.forEach(provider => {
    if (provider.nameKey.toLowerCase().includes(searchTerm)) {
      suggestions.push(provider.nameKey);
    }
    
    provider.services.forEach(service => {
      if (service.nameKey.toLowerCase().includes(searchTerm)) {
        suggestions.push(service.nameKey);
      }
    });
  });
  
  // Remove duplicates and limit to 5 suggestions
  return [...new Set(suggestions)].slice(0, 5);
}

/**
 * Get popular search terms
 */
export async function getPopularSearches(): Promise<ApiResponse<string[]>> {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300));
    
    // Mock popular searches - in a real app, this would come from analytics
    const popularSearches = [
      "cleaning",
      "plumbing",
      "electrical",
      "haircut",
      "massage",
      "tutoring",
      "photography",
      "car wash"
    ];
    
    return {
      success: true,
      data: popularSearches
    };
  } catch (error) {
    return {
      success: false,
      error: "Failed to get popular searches",
      message: "An error occurred while fetching popular searches"
    };
  }
}
