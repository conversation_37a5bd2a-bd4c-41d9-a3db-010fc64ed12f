import React, { useState } from "react";
import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  SafeAreaView,
  TouchableOpacity,
  Alert,
} from "react-native";

export default function BrowseScreen() {
  const [selectedCategory, setSelectedCategory] = useState("All Categories");

  const handleSearch = () => {
    Alert.alert("Search", "Enter your search term:", [
      { text: "Cleaning", onPress: () => Alert.alert("Search Results", "Found 8 cleaning services near you") },
      { text: "Electrical", onPress: () => Alert.alert("Search Results", "Found 5 electrical services near you") },
      { text: "Beauty", onPress: () => Alert.alert("Search Results", "Found 12 beauty services near you") },
      { text: "Cancel", style: "cancel" },
    ]);
  };

  const handleCategoryFilter = () => {
    Alert.alert("Filter by Category", "Select a category:", [
      { text: "Home Services", onPress: () => setSelectedCategory("Home Services") },
      { text: "Personal Care", onPress: () => setSelectedCategory("Personal Care") },
      { text: "Professional", onPress: () => setSelectedCategory("Professional") },
      { text: "All Categories", onPress: () => setSelectedCategory("All Categories") },
      { text: "Cancel", style: "cancel" },
    ]);
  };

  const handleFilters = () => {
    Alert.alert("Filters", "Filter options:", [
      { text: "Price: Low to High", onPress: () => Alert.alert("Sorted", "Providers sorted by price") },
      { text: "Rating: High to Low", onPress: () => Alert.alert("Sorted", "Providers sorted by rating") },
      { text: "Distance: Nearest", onPress: () => Alert.alert("Sorted", "Providers sorted by distance") },
      { text: "Cancel", style: "cancel" },
    ]);
  };

  const handleProviderPress = (providerName: string) => {
    Alert.alert("Provider Details", `${providerName}`, [
      { text: "View Profile", onPress: () => Alert.alert("Profile", `Viewing ${providerName} profile`) },
      { text: "Book Now", onPress: () => handleBooking(providerName) },
      { text: "Message", onPress: () => Alert.alert("Message", `Starting chat with ${providerName}`) },
      { text: "Cancel", style: "cancel" },
    ]);
  };

  const handleBooking = (providerName: string) => {
    Alert.alert("Book Service", `Book ${providerName}?`, [
      { text: "Confirm", onPress: () => Alert.alert("Success!", "Booking confirmed! Check your bookings tab.") },
      { text: "Cancel", style: "cancel" },
    ]);
  };

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollView}>
        <View style={styles.header}>
          <Text style={styles.title}>Browse Providers</Text>

          {/* Search Bar */}
          <TouchableOpacity style={styles.searchBar} onPress={handleSearch} activeOpacity={0.7}>
            <Text style={styles.searchIcon}>🔍</Text>
            <Text style={styles.searchPlaceholder}>Search services or providers...</Text>
          </TouchableOpacity>

          {/* Filters */}
          <View style={styles.filtersContainer}>
            <TouchableOpacity style={styles.categoryFilter} onPress={handleCategoryFilter} activeOpacity={0.7}>
              <Text style={styles.filterText}>{selectedCategory} ▼</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.filterButton} onPress={handleFilters} activeOpacity={0.7}>
              <Text style={styles.filterIcon}>⚙️</Text>
              <Text style={styles.filterButtonText}>Filters</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Results */}
        <View style={styles.resultsContainer}>
          <Text style={styles.resultsCount}>12 providers found</Text>

          {/* Provider Cards */}
          <TouchableOpacity style={styles.providerCard} onPress={() => handleProviderPress("Thabo's Cleaning Services")} activeOpacity={0.8}>
            <View style={styles.providerHeader}>
              <View style={styles.providerAvatar} />
              <View style={styles.providerInfo}>
                <Text style={styles.providerName}>Thabo's Cleaning Services ✓</Text>
                <Text style={styles.providerLocation}>📍 Cape Town • 3.2 km</Text>
              </View>
            </View>
            <Text style={styles.providerDescription}>Professional cleaning services with 5+ years of experience.</Text>
            <View style={styles.providerFooter}>
              <Text style={styles.rating}>⭐ 4.8 (124)</Text>
              <View style={styles.priceContainer}>
                <Text style={styles.priceLabel}>From</Text>
                <Text style={styles.price}>R350</Text>
              </View>
            </View>
          </TouchableOpacity>

          <TouchableOpacity style={styles.providerCard} onPress={() => handleProviderPress("Sipho's Electrical")} activeOpacity={0.8}>
            <View style={styles.providerHeader}>
              <View style={styles.providerAvatar} />
              <View style={styles.providerInfo}>
                <Text style={styles.providerName}>Sipho's Electrical ✓</Text>
                <Text style={styles.providerLocation}>📍 Durban • 4.1 km</Text>
              </View>
            </View>
            <Text style={styles.providerDescription}>Licensed electrician with 10+ years of experience.</Text>
            <View style={styles.providerFooter}>
              <Text style={styles.rating}>⭐ 4.9 (156)</Text>
              <View style={styles.priceContainer}>
                <Text style={styles.priceLabel}>From</Text>
                <Text style={styles.price}>R450</Text>
              </View>
            </View>
          </TouchableOpacity>

          <TouchableOpacity style={styles.providerCard}>
            <View style={styles.providerHeader}>
              <View style={styles.providerAvatar} />
              <View style={styles.providerInfo}>
                <Text style={styles.providerName}>Lerato's Beauty ✓</Text>
                <Text style={styles.providerLocation}>📍 Pretoria • 2.8 km</Text>
              </View>
            </View>
            <Text style={styles.providerDescription}>Certified beautician specializing in natural hair care.</Text>
            <View style={styles.providerFooter}>
              <Text style={styles.rating}>⭐ 4.7 (92)</Text>
              <View style={styles.priceContainer}>
                <Text style={styles.priceLabel}>From</Text>
                <Text style={styles.price}>R280</Text>
              </View>
            </View>
          </TouchableOpacity>

          <TouchableOpacity style={styles.providerCard}>
            <View style={styles.providerHeader}>
              <View style={styles.providerAvatar} />
              <View style={styles.providerInfo}>
                <Text style={styles.providerName}>Nomsa's Catering ✓</Text>
                <Text style={styles.providerLocation}>📍 Johannesburg • 5.1 km</Text>
              </View>
            </View>
            <Text style={styles.providerDescription}>Traditional and modern catering for all occasions.</Text>
            <View style={styles.providerFooter}>
              <Text style={styles.rating}>⭐ 4.6 (78)</Text>
              <View style={styles.priceContainer}>
                <Text style={styles.priceLabel}>From</Text>
                <Text style={styles.price}>R180</Text>
              </View>
            </View>
          </TouchableOpacity>

          <TouchableOpacity style={styles.providerCard}>
            <View style={styles.providerHeader}>
              <View style={styles.providerAvatar} />
              <View style={styles.providerInfo}>
                <Text style={styles.providerName}>David's Plumbing ✓</Text>
                <Text style={styles.providerLocation}>📍 Cape Town • 2.9 km</Text>
              </View>
            </View>
            <Text style={styles.providerDescription}>Emergency plumbing services available 24/7.</Text>
            <View style={styles.providerFooter}>
              <Text style={styles.rating}>⭐ 4.9 (203)</Text>
              <View style={styles.priceContainer}>
                <Text style={styles.priceLabel}>From</Text>
                <Text style={styles.price}>R320</Text>
              </View>
            </View>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
}



const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F9FAFB",
  },
  scrollView: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 60, // Increased for full-screen iPhone
    paddingBottom: 16,
  },
  title: {
    fontSize: 28,
    fontWeight: "bold",
    color: "#1F2937",
    marginBottom: 20,
  },
  searchBar: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#FFFFFF",
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  searchIcon: {
    fontSize: 16,
    marginRight: 8,
  },
  searchPlaceholder: {
    fontSize: 16,
    color: "#6B7280",
    flex: 1,
  },
  filtersContainer: {
    flexDirection: "row",
    gap: 12,
    marginBottom: 16,
  },
  categoryFilter: {
    flex: 1,
    backgroundColor: "#FFFFFF",
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  filterText: {
    fontSize: 14,
    color: "#1F2937",
    fontWeight: "500",
  },
  filterButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#FFFFFF",
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  filterIcon: {
    fontSize: 16,
    marginRight: 8,
  },
  filterButtonText: {
    fontSize: 14,
    color: "#1F2937",
    fontWeight: "500",
  },
  resultsContainer: {
    paddingHorizontal: 20,
  },
  resultsCount: {
    fontSize: 16,
    color: "#6B7280",
    marginBottom: 16,
  },
  providerCard: {
    backgroundColor: "#FFFFFF",
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  providerHeader: {
    flexDirection: "row",
    marginBottom: 12,
  },
  providerAvatar: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: "#F3F4F6",
    marginRight: 12,
  },
  providerInfo: {
    flex: 1,
  },
  providerName: {
    fontSize: 18,
    fontWeight: "600",
    color: "#1F2937",
    marginBottom: 4,
  },
  providerLocation: {
    fontSize: 14,
    color: "#6B7280",
  },
  providerDescription: {
    fontSize: 14,
    color: "#6B7280",
    marginBottom: 12,
    lineHeight: 20,
  },
  providerFooter: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  rating: {
    fontSize: 14,
    fontWeight: "500",
    color: "#1F2937",
  },
  priceContainer: {
    alignItems: "flex-end",
  },
  priceLabel: {
    fontSize: 12,
    color: "#6B7280",
  },
  price: {
    fontSize: 16,
    fontWeight: "600",
    color: "#10B981",
  },
});