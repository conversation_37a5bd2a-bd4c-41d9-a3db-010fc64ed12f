import React, { useState, useEffect } from "react";
import {
  StyleSheet,
  Text,
  View,
  FlatList,
  TouchableOpacity,
  SafeAreaView,
  ActivityIndicator,
} from "react-native";
import { router, useLocalSearchParams } from "expo-router";
import colors from "@/constants/colors";
import typography from "@/constants/typography";
import { useTranslation } from "@/hooks/useTranslation";
import { useCategories, useProviders } from "@/hooks/useApi";
import SearchBar from "@/components/SearchBar";
import ProviderCard from "@/components/ProviderCard";
import CategoryDropdown from "@/components/CategoryDropdown";
import { Filter } from "lucide-react-native";

export default function BrowseScreen() {
  const { t } = useTranslation();
  const params = useLocalSearchParams();

  const [searchQuery, setSearchQuery] = useState((params.search as string) || "");
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  // Fetch data using API hooks
  const { data: categoriesData } = useCategories();
  const { data: providersData, loading: providersLoading, refetch } = useProviders({
    search: searchQuery,
    categoryId: selectedCategory || undefined,
  });

  const categories = categoriesData?.categories || [];
  const filteredProviders = providersData?.providers || [];

  const handleProviderPress = (provider: any) => {
    router.push(`/provider/${provider.id}`);
  };

  const handleSearchSubmit = () => {
    refetch();
  };

  const handleSearchClear = () => {
    setSearchQuery("");
  };

  const handleCategoryChange = (categoryId: string | null) => {
    setSelectedCategory(categoryId);
  };

  const renderProviderItem = ({ item }: { item: any }) => (
    <ProviderCard provider={item} onPress={handleProviderPress} />
  );

  const renderHeader = () => (
    <View style={styles.header}>
      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <SearchBar
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholder={t("searchServicesOrProviders")}
          onSubmit={handleSearchSubmit}
          onClear={handleSearchClear}
        />
      </View>

      {/* Filters */}
      <View style={styles.filtersContainer}>
        <CategoryDropdown
          categories={categories}
          selectedCategory={selectedCategory ? categories.find(cat => cat.id === selectedCategory) || null : null}
          onSelect={(category) => handleCategoryChange(category?.id || null)}
        />
        <TouchableOpacity style={styles.filterButton}>
          <Filter size={20} color={colors.textSecondary} />
          <Text style={styles.filterButtonText}>{t("filters")}</Text>
        </TouchableOpacity>
      </View>

      {/* Results Count */}
      <View style={styles.resultsContainer}>
        <Text style={styles.resultsText}>
          {filteredProviders.length} {filteredProviders.length === 1 ? t("result") : t("results")}
          {selectedCategory && (
            <Text style={styles.categoryText}>
              {" "}in {t(categories.find(cat => cat.id === selectedCategory)?.nameKey || "")}
            </Text>
          )}
        </Text>
      </View>
    </View>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Text style={styles.emptyTitle}>{t("noProvidersFound")}</Text>
      <Text style={styles.emptySubtitle}>{t("tryAdjustingSearch")}</Text>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {providersLoading ? (
        <View style={styles.loadingScreen}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>Loading providers...</Text>
        </View>
      ) : (
        <FlatList
          data={filteredProviders}
          renderItem={renderProviderItem}
          keyExtractor={(item) => item.id}
          ListHeaderComponent={renderHeader}
          ListEmptyComponent={renderEmptyState}
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
        />
      )}
    </SafeAreaView>
  );
}



const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  listContainer: {
    flexGrow: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 16,
  },
  searchContainer: {
    marginBottom: 16,
  },
  filtersContainer: {
    flexDirection: "row",
    gap: 12,
    marginBottom: 16,
  },
  filterButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: colors.card,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  filterButtonText: {
    ...typography.bodySmall,
    color: colors.textSecondary,
  },
  resultsContainer: {
    marginBottom: 8,
  },
  resultsText: {
    ...typography.bodySmall,
    color: colors.textSecondary,
  },
  categoryText: {
    color: colors.primary,
    fontWeight: "500",
  },
  emptyState: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 40,
    paddingTop: 60,
  },
  emptyTitle: {
    ...typography.h3,
    textAlign: "center",
    marginBottom: 8,
  },
  emptySubtitle: {
    ...typography.body,
    color: colors.textSecondary,
    textAlign: "center",
  },
  loadingScreen: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    gap: 16,
  },
  loadingText: {
    ...typography.body,
    color: colors.textSecondary,
  },
});