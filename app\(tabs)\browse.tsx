import React from "react";
import {
  StyleSheet,
  Text,
  View,
  <PERSON><PERSON>View,
  SafeAreaView,
} from "react-native";

export default function BrowseScreen() {
  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Browse Services</Text>
          <Text style={styles.subtitle}>Find service providers in your area</Text>
        </View>

        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <View style={styles.searchBar}>
            <Text style={styles.searchIcon}>🔍</Text>
            <Text style={styles.searchPlaceholder}>Search services or providers...</Text>
          </View>
        </View>

        {/* Categories */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Categories</Text>
          <View style={styles.categoryGrid}>
            <View style={styles.categoryItem}>
              <Text style={styles.categoryEmoji}>🏠</Text>
              <Text style={styles.categoryName}>Home Services</Text>
            </View>
            <View style={styles.categoryItem}>
              <Text style={styles.categoryEmoji}>✂️</Text>
              <Text style={styles.categoryName}>Personal Care</Text>
            </View>
            <View style={styles.categoryItem}>
              <Text style={styles.categoryEmoji}>💼</Text>
              <Text style={styles.categoryName}>Professional</Text>
            </View>
            <View style={styles.categoryItem}>
              <Text style={styles.categoryEmoji}>📚</Text>
              <Text style={styles.categoryName}>Education</Text>
            </View>
          </View>
        </View>

        {/* Providers */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>All Providers</Text>

          <View style={styles.providerCard}>
            <View style={styles.providerHeader}>
              <View style={styles.providerAvatar} />
              <View style={styles.providerInfo}>
                <Text style={styles.providerName}>Thabo's Cleaning Services ✓</Text>
                <Text style={styles.providerLocation}>📍 Cape Town • 3.2 km</Text>
              </View>
            </View>
            <Text style={styles.providerDescription}>Professional cleaning services with 5+ years of experience.</Text>
            <View style={styles.providerFooter}>
              <Text style={styles.rating}>⭐ 4.8 (124)</Text>
              <Text style={styles.price}>From R350</Text>
            </View>
          </View>

          <View style={styles.providerCard}>
            <View style={styles.providerHeader}>
              <View style={styles.providerAvatar} />
              <View style={styles.providerInfo}>
                <Text style={styles.providerName}>Sipho's Electrical ✓</Text>
                <Text style={styles.providerLocation}>📍 Durban • 4.1 km</Text>
              </View>
            </View>
            <Text style={styles.providerDescription}>Licensed electrician with 10+ years of experience.</Text>
            <View style={styles.providerFooter}>
              <Text style={styles.rating}>⭐ 4.9 (156)</Text>
              <Text style={styles.price}>From R450</Text>
            </View>
          </View>

          <View style={styles.providerCard}>
            <View style={styles.providerHeader}>
              <View style={styles.providerAvatar} />
              <View style={styles.providerInfo}>
                <Text style={styles.providerName}>Lerato's Beauty ✓</Text>
                <Text style={styles.providerLocation}>📍 Pretoria • 2.8 km</Text>
              </View>
            </View>
            <Text style={styles.providerDescription}>Certified beautician specializing in natural hair care.</Text>
            <View style={styles.providerFooter}>
              <Text style={styles.rating}>⭐ 4.7 (92)</Text>
              <Text style={styles.price}>From R280</Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F9FAFB",
  },
  scrollView: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 16,
  },
  title: {
    fontSize: 28,
    fontWeight: "bold",
    color: "#1F2937",
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    color: "#6B7280",
  },
  searchContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  searchBar: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#FFFFFF",
    borderRadius: 8,
    padding: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  searchIcon: {
    fontSize: 16,
    marginRight: 8,
  },
  searchPlaceholder: {
    fontSize: 16,
    color: "#6B7280",
    flex: 1,
  },
  section: {
    marginBottom: 24,
    paddingHorizontal: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: "600",
    color: "#1F2937",
    marginBottom: 16,
  },
  categoryGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
  },
  categoryItem: {
    width: "48%",
    backgroundColor: "#FFFFFF",
    borderRadius: 12,
    padding: 16,
    alignItems: "center",
    marginBottom: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  categoryEmoji: {
    fontSize: 32,
    marginBottom: 8,
  },
  categoryName: {
    fontSize: 14,
    fontWeight: "500",
    color: "#1F2937",
    textAlign: "center",
  },
  providerCard: {
    backgroundColor: "#FFFFFF",
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  providerHeader: {
    flexDirection: "row",
    marginBottom: 12,
  },
  providerAvatar: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: "#F3F4F6",
    marginRight: 12,
  },
  providerInfo: {
    flex: 1,
  },
  providerName: {
    fontSize: 18,
    fontWeight: "600",
    color: "#1F2937",
    marginBottom: 4,
  },
  providerLocation: {
    fontSize: 14,
    color: "#6B7280",
  },
  providerDescription: {
    fontSize: 14,
    color: "#6B7280",
    marginBottom: 12,
    lineHeight: 20,
  },
  providerFooter: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  rating: {
    fontSize: 14,
    fontWeight: "500",
    color: "#1F2937",
  },
  price: {
    fontSize: 16,
    fontWeight: "600",
    color: "#10B981",
  },
});