// Authentication API endpoints
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface User {
  id: string;
  email: string;
  fullName: string;
  phoneNumber?: string;
  avatar?: string;
  location?: string;
  verified: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface AuthResponse {
  user: User;
  token: string;
  refreshToken: string;
  expiresIn: number;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  password: string;
  fullName: string;
  phoneNumber?: string;
}

export interface UpdateProfileData {
  fullName?: string;
  phoneNumber?: string;
  avatar?: string;
  location?: string;
}

// Mock user data
const mockUsers: User[] = [
  {
    id: "user1",
    email: "<EMAIL>",
    fullName: "John Doe",
    phoneNumber: "+27123456789",
    avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&auto=format&fit=crop&w=200&q=80",
    location: "Cape Town, South Africa",
    verified: true,
    createdAt: "2024-01-15T10:30:00Z",
    updatedAt: "2024-01-20T14:45:00Z"
  }
];

/**
 * Login user with email and password
 */
export async function login(credentials: LoginCredentials): Promise<ApiResponse<AuthResponse>> {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const { email, password } = credentials;
    
    // Basic validation
    if (!email || !password) {
      return {
        success: false,
        error: "Missing credentials",
        message: "Email and password are required"
      };
    }
    
    // Mock authentication - in a real app, this would validate against a database
    const user = mockUsers.find(u => u.email.toLowerCase() === email.toLowerCase());
    
    if (!user) {
      return {
        success: false,
        error: "Invalid credentials",
        message: "Email or password is incorrect"
      };
    }
    
    // Mock password validation (in real app, compare hashed passwords)
    if (password !== "password123") {
      return {
        success: false,
        error: "Invalid credentials",
        message: "Email or password is incorrect"
      };
    }
    
    // Generate mock tokens
    const token = generateMockToken(user.id);
    const refreshToken = generateMockToken(user.id, true);
    
    return {
      success: true,
      data: {
        user,
        token,
        refreshToken,
        expiresIn: 3600 // 1 hour
      }
    };
  } catch (error) {
    return {
      success: false,
      error: "Login failed",
      message: "An error occurred during login"
    };
  }
}

/**
 * Register a new user
 */
export async function register(userData: RegisterData): Promise<ApiResponse<AuthResponse>> {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1200));
    
    const { email, password, fullName, phoneNumber } = userData;
    
    // Basic validation
    if (!email || !password || !fullName) {
      return {
        success: false,
        error: "Missing required fields",
        message: "Email, password, and full name are required"
      };
    }
    
    // Check if user already exists
    const existingUser = mockUsers.find(u => u.email.toLowerCase() === email.toLowerCase());
    if (existingUser) {
      return {
        success: false,
        error: "User already exists",
        message: "An account with this email already exists"
      };
    }
    
    // Create new user
    const newUser: User = {
      id: `user${Date.now()}`,
      email: email.toLowerCase(),
      fullName,
      phoneNumber,
      verified: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    // Add to mock database
    mockUsers.push(newUser);
    
    // Generate mock tokens
    const token = generateMockToken(newUser.id);
    const refreshToken = generateMockToken(newUser.id, true);
    
    return {
      success: true,
      data: {
        user: newUser,
        token,
        refreshToken,
        expiresIn: 3600 // 1 hour
      }
    };
  } catch (error) {
    return {
      success: false,
      error: "Registration failed",
      message: "An error occurred during registration"
    };
  }
}

/**
 * Get current user profile
 */
export async function getCurrentUser(token: string): Promise<ApiResponse<User>> {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Mock token validation
    const userId = validateMockToken(token);
    if (!userId) {
      return {
        success: false,
        error: "Invalid token",
        message: "Authentication token is invalid or expired"
      };
    }
    
    const user = mockUsers.find(u => u.id === userId);
    if (!user) {
      return {
        success: false,
        error: "User not found",
        message: "User account no longer exists"
      };
    }
    
    return {
      success: true,
      data: user
    };
  } catch (error) {
    return {
      success: false,
      error: "Failed to get user",
      message: "An error occurred while fetching user data"
    };
  }
}

/**
 * Update user profile
 */
export async function updateProfile(token: string, updateData: UpdateProfileData): Promise<ApiResponse<User>> {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 800));
    
    // Mock token validation
    const userId = validateMockToken(token);
    if (!userId) {
      return {
        success: false,
        error: "Invalid token",
        message: "Authentication token is invalid or expired"
      };
    }
    
    const userIndex = mockUsers.findIndex(u => u.id === userId);
    if (userIndex === -1) {
      return {
        success: false,
        error: "User not found",
        message: "User account no longer exists"
      };
    }
    
    // Update user data
    const updatedUser = {
      ...mockUsers[userIndex],
      ...updateData,
      updatedAt: new Date().toISOString()
    };
    
    mockUsers[userIndex] = updatedUser;
    
    return {
      success: true,
      data: updatedUser
    };
  } catch (error) {
    return {
      success: false,
      error: "Update failed",
      message: "An error occurred while updating profile"
    };
  }
}

/**
 * Logout user (invalidate token)
 */
export async function logout(token: string): Promise<ApiResponse<void>> {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300));
    
    // In a real app, you would invalidate the token on the server
    // For this mock, we just return success
    
    return {
      success: true,
      message: "Logged out successfully"
    };
  } catch (error) {
    return {
      success: false,
      error: "Logout failed",
      message: "An error occurred during logout"
    };
  }
}

/**
 * Refresh authentication token
 */
export async function refreshToken(refreshToken: string): Promise<ApiResponse<{ token: string; expiresIn: number }>> {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 400));
    
    // Mock refresh token validation
    const userId = validateMockToken(refreshToken);
    if (!userId) {
      return {
        success: false,
        error: "Invalid refresh token",
        message: "Refresh token is invalid or expired"
      };
    }
    
    // Generate new access token
    const newToken = generateMockToken(userId);
    
    return {
      success: true,
      data: {
        token: newToken,
        expiresIn: 3600 // 1 hour
      }
    };
  } catch (error) {
    return {
      success: false,
      error: "Token refresh failed",
      message: "An error occurred while refreshing token"
    };
  }
}

// Helper functions for mock token handling
function generateMockToken(userId: string, isRefreshToken = false): string {
  const prefix = isRefreshToken ? "refresh" : "access";
  const timestamp = Date.now();
  return `${prefix}_${userId}_${timestamp}`;
}

function validateMockToken(token: string): string | null {
  try {
    const parts = token.split("_");
    if (parts.length !== 3) return null;
    
    const [type, userId, timestamp] = parts;
    const tokenAge = Date.now() - parseInt(timestamp);
    
    // Mock token expiry: access tokens expire in 1 hour, refresh tokens in 7 days
    const maxAge = type === "refresh" ? 7 * 24 * 60 * 60 * 1000 : 60 * 60 * 1000;
    
    if (tokenAge > maxAge) return null;
    
    return userId;
  } catch {
    return null;
  }
}
